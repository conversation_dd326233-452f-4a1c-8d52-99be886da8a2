# 🚀 FlowUI Vercel Deployment Guide

## Quick Deployment Steps

### 1. Prepare Your Repository
```bash
# Make sure all changes are committed
git add .
git commit -m "Ready for production deployment"
git push origin main
```

### 2. Deploy to Vercel

#### Option A: Vercel CLI (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Follow the prompts:
# - Link to existing project? No
# - What's your project's name? flowui (or your preferred name)
# - In which directory is your code located? ./
# - Want to override the settings? No
```

#### Option B: Vercel Dashboard
1. Go to [vercel.com/new](https://vercel.com/new)
2. Import your Git repository
3. Vercel will auto-detect Next.js - no configuration needed!
   - **Framework Preset**: Next.js (auto-detected)
   - **Build Command**: `npm run build` (auto-detected)
   - **Output Directory**: Leave empty (auto-detected)
   - **Install Command**: `npm install` (auto-detected)

### 3. Set Environment Variables in Vercel

In your Vercel project dashboard, go to **Settings** → **Environment Variables** and add:

```bash
# Production Clerk Keys (get from Clerk Dashboard)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_actual_key_here
CLERK_SECRET_KEY=sk_live_your_actual_secret_key_here

# Clerk URLs
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/dashboard
NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/dashboard
```

### 4. Update Clerk Dashboard for Production

1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Navigate to **Domains**
3. Add your Vercel domain (e.g., `your-project.vercel.app`)
4. Verify **Waitlist** mode is still enabled in **Restrictions**

### 5. Test Your Deployment

Visit your deployed URL and test:
- [ ] Home page loads
- [ ] Waitlist form works (`/waitlist`)
- [ ] Sign-in/Sign-up flows work
- [ ] Dashboard redirects work
- [ ] Success page shows after waitlist signup

## Troubleshooting

### Build Fails
- Check build logs in Vercel dashboard
- Ensure all TypeScript errors are fixed
- Verify all dependencies are in package.json

### Authentication Issues
- Verify environment variables are set correctly
- Check that domain is added in Clerk dashboard
- Ensure you're using production keys (pk_live_ and sk_live_)

### Waitlist Not Working
- Confirm Waitlist mode is enabled in Clerk dashboard
- Check that waitlistUrl prop is set in ClerkProvider
- Verify afterJoinWaitlistUrl path exists

## Custom Domain (Optional)

1. In Vercel dashboard: **Settings** → **Domains**
2. Add your custom domain
3. Configure DNS as instructed
4. Update Clerk dashboard with new domain

## Environment Variables Checklist

- [ ] `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` (starts with pk_live_)
- [ ] `CLERK_SECRET_KEY` (starts with sk_live_)
- [ ] `NEXT_PUBLIC_CLERK_SIGN_IN_URL`
- [ ] `NEXT_PUBLIC_CLERK_SIGN_UP_URL`
- [ ] `NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL`
- [ ] `NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL`

## Post-Deployment

1. **Monitor**: Check Vercel analytics and logs
2. **Security**: Review Clerk security settings
3. **Performance**: Monitor Core Web Vitals
4. **Backup**: Set up regular database backups (if applicable)

Your FlowUI SaaS is now live! 🎉
