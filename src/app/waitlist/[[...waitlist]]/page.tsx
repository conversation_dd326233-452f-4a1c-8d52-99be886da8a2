import { Waitlist } from '@clerk/nextjs'

export default function WaitlistPage() {
  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="w-full max-w-md p-6">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Join the Waitlist
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Be the first to know when FlowUI launches. Get early access to create stunning UI interfaces for your N8N automations.
          </p>
        </div>
        <Waitlist 
          afterJoinWaitlistUrl="/waitlist-success"
          signInUrl="/sign-in"
        />
      </div>
    </div>
  )
}
