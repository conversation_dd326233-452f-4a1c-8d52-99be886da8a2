import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function WaitlistSuccessPage() {
  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="w-full max-w-md p-6 text-center">
        <div className="mb-8">
          <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
            <svg 
              className="w-8 h-8 text-green-600 dark:text-green-400" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M5 13l4 4L19 7" 
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            You're on the list!
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Thank you for joining the FlowUI waitlist. We'll notify you as soon as we launch with early access and exclusive updates.
          </p>
        </div>
        
        <div className="space-y-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            In the meantime, follow us for updates:
          </p>
          
          <div className="flex justify-center space-x-4">
            <Link href="/" className="text-indigo-600 hover:text-indigo-500">
              <Button variant="outline">
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
