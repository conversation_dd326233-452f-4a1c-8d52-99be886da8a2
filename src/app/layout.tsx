import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import "./globals.css";
import Header from "./_components/Header";

export const metadata: Metadata = {
  title: "Clerk Next.js Quickstart",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          <Header />
          {children}
        </body>
      </html>
    </ClerkProvider>
  );
}
