'use client'
import { useState } from 'react';
import { useClerk } from '@clerk/nextjs';

function Waitlist() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { client } = useClerk();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await client.signUp.create({
        emailAddress: email,
      });
      setIsSubmitted(true);
    } catch (error) {
      console.error('Error submitting to waitlist:', error);
    }
  };

  return (
    <div className="mt-8">
      {isSubmitted ? (
        <p className="text-lg text-green-600">
          Thank you for joining the waitlist!
        </p>
      ) : (
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <div id="clerk-captcha"></div>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            className="flex-grow rounded border border-gray-300 px-4 py-2 focus:border-indigo-500 focus:outline-none"
            required
          />
          <button
            type="submit"
            className="rounded bg-indigo-600 px-5 py-3 font-medium text-white shadow-sm transition-colors hover:bg-indigo-700"
          >
            Join Waitlist
          </button>
        </form>
      )}
    </div>
  );
}

export default Waitlist;