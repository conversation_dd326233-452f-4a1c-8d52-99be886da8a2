import { But<PERSON> } from '@/components/ui/button'
import React from 'react'
import { SignedIn, SignedOut, UserButton } from "@clerk/nextjs";
import Link from 'next/link';

function Header() {
  return (
    <div className='p-5 border shadow-sm'>
        <div className='flex justify-between items-center'>
            <img src="/logo.svg" alt="Logo" />
            <div className='flex gap-6'>
            <SignedIn>
              <Link href="/dashboard">
                <Button>Dashboard</Button>
              </Link>
              <UserButton />
            </SignedIn>
            <SignedOut>
              <Link href="/waitlist">
                <Button variant="outline">Join Waitlist</Button>
              </Link>
              <Link href="/sign-in">
                <Button>Sign In</Button>
              </Link>
              <Link href="/sign-up">
                <Button>Sign Up</Button>
              </Link>
            </SignedOut>
            </div>
        </div>
    </div>
  )
}

export default Header