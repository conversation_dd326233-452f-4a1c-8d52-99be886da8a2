# FlowUI Deployment Guide

This guide will help you deploy your FlowUI SaaS application to Vercel with Clerk authentication in production.

## Prerequisites

- [Vercel Account](https://vercel.com)
- [Clerk Account](https://clerk.com)
- Git repository (GitHub, GitLab, or Bitbucket)

## Step 1: Set Up Production Clerk Instance

### 1.1 Create Production Application in Clerk

1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Click "Add application" or create a new application
3. Choose "Production" environment
4. Name it "FlowUI Production" (or your preferred name)
5. Select the authentication methods you want (Email, Google, GitHub, etc.)

### 1.2 Configure Clerk Settings

1. **Enable Waitlist Mode** (Important for your waitlist feature):
   - Go to **User & Authentication** → **Restrictions**
   - Under "Sign-up modes", enable **Waitlist**

2. **Set up Domains**:
   - Go to **Domains**
   - Add your production domain (e.g., `flowui.vercel.app` or your custom domain)

3. **Configure Redirects**:
   - Go to **Paths**
   - Set Sign-in URL: `/sign-in`
   - Set Sign-up URL: `/sign-up`
   - Set After sign-in: `/dashboard` (or your preferred redirect)
   - Set After sign-up: `/dashboard` (or your preferred redirect)

### 1.3 Get Production Keys

1. In your Clerk dashboard, go to **API Keys**
2. Copy the **Publishable key** (starts with `pk_live_`)
3. Copy the **Secret key** (starts with `sk_live_`)

## Step 2: Deploy to Vercel

### 2.1 Connect Repository to Vercel

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your Git repository
4. Select the repository containing your FlowUI code

### 2.2 Configure Build Settings

Vercel should auto-detect Next.js. Verify these settings:
- **Framework Preset**: Next.js
- **Build Command**: `npm run build`
- **Output Directory**: Leave empty (Next.js default)
- **Install Command**: `npm install`

### 2.3 Set Environment Variables

In the Vercel deployment configuration, add these environment variables:

```bash
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_actual_publishable_key
CLERK_SECRET_KEY=sk_live_your_actual_secret_key
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/
NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/
```

**Important**: Replace the placeholder keys with your actual production keys from Clerk.

### 2.4 Deploy

1. Click "Deploy"
2. Wait for the build to complete
3. Your app will be available at `https://your-project-name.vercel.app`

## Step 3: Update Clerk with Production Domain

1. Go back to your Clerk dashboard
2. Navigate to **Domains**
3. Add your Vercel domain (e.g., `your-project-name.vercel.app`)
4. If using a custom domain, add that as well

## Step 4: Test Production Deployment

### 4.1 Test Authentication Flow

1. Visit your production URL
2. Test the sign-up process
3. Test the sign-in process
4. Test the waitlist functionality
5. Verify redirects work correctly

### 4.2 Test Waitlist Feature

1. Go to `/waitlist` on your production site
2. Try joining the waitlist
3. Verify the success page works
4. Check Clerk dashboard to see waitlist entries

## Step 5: Custom Domain (Optional)

### 5.1 Add Custom Domain to Vercel

1. In Vercel dashboard, go to your project
2. Go to **Settings** → **Domains**
3. Add your custom domain
4. Follow DNS configuration instructions

### 5.2 Update Clerk Configuration

1. In Clerk dashboard, add your custom domain
2. Update environment variables if needed

## Troubleshooting

### Common Issues

1. **Clerk Authentication Not Working**:
   - Verify environment variables are set correctly
   - Check that domain is added in Clerk dashboard
   - Ensure you're using production keys (pk_live_ and sk_live_)

2. **Waitlist Not Working**:
   - Ensure Waitlist mode is enabled in Clerk dashboard
   - Verify the waitlistUrl prop is set in ClerkProvider

3. **Build Failures**:
   - Check for TypeScript errors
   - Verify all dependencies are in package.json
   - Check build logs in Vercel dashboard

### Environment Variables Checklist

- [ ] NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY (starts with pk_live_)
- [ ] CLERK_SECRET_KEY (starts with sk_live_)
- [ ] NEXT_PUBLIC_CLERK_SIGN_IN_URL
- [ ] NEXT_PUBLIC_CLERK_SIGN_UP_URL
- [ ] NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL
- [ ] NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL

### Important Files Checklist

- [ ] `src/middleware.ts` - Clerk middleware (not in root directory)
- [ ] `src/app/layout.tsx` - ClerkProvider with waitlistUrl prop
- [ ] `src/app/waitlist/[[...waitlist]]/page.tsx` - Waitlist page
- [ ] `src/app/dashboard/page.tsx` - Dashboard for authenticated users

## Security Notes

- Never commit `.env.local` or `.env` files to version control
- Use Vercel's environment variables feature for production secrets
- Regularly rotate your Clerk secret keys
- Enable webhook signing for production (if using webhooks)

## Next Steps After Deployment

1. Set up monitoring and analytics
2. Configure custom email templates in Clerk
3. Set up webhook endpoints for user events
4. Implement proper error handling and logging
5. Set up backup and disaster recovery procedures

---

Your FlowUI SaaS is now ready for production! 🚀
