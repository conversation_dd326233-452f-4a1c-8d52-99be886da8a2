#!/usr/bin/env node

/**
 * Pre-deployment check script for FlowUI
 * Validates environment variables and configuration before deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 FlowUI Deployment Check\n');

// Check if required files exist
const requiredFiles = [
  'package.json',
  'next.config.ts',
  'middleware.ts',
  '.env.example',
  'src/app/layout.tsx',
  'src/app/waitlist/[[...waitlist]]/page.tsx',
  'src/app/dashboard/page.tsx'
];

console.log('📁 Checking required files...');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log(`\n❌ Missing ${missingFiles.length} required files. Please ensure all files are present before deployment.`);
  process.exit(1);
}

// Check package.json for required dependencies
console.log('\n📦 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = [
  '@clerk/nextjs',
  'next',
  'react',
  'react-dom'
];

let missingDeps = [];
requiredDeps.forEach(dep => {
  if (packageJson.dependencies[dep]) {
    console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
  } else {
    console.log(`❌ ${dep} - MISSING`);
    missingDeps.push(dep);
  }
});

if (missingDeps.length > 0) {
  console.log(`\n❌ Missing ${missingDeps.length} required dependencies. Run 'npm install' to install missing packages.`);
  process.exit(1);
}

// Check environment variables template
console.log('\n🔐 Environment Variables Checklist:');
console.log('Make sure to set these in Vercel:');
console.log('');
console.log('Required for Production:');
console.log('- NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY (pk_live_...)');
console.log('- CLERK_SECRET_KEY (sk_live_...)');
console.log('- NEXT_PUBLIC_CLERK_SIGN_IN_URL');
console.log('- NEXT_PUBLIC_CLERK_SIGN_UP_URL');
console.log('- NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL');
console.log('- NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL');

console.log('\n📋 Pre-deployment Checklist:');
console.log('');
console.log('Clerk Setup:');
console.log('□ Created production Clerk application');
console.log('□ Enabled Waitlist mode in Clerk dashboard');
console.log('□ Added production domain to Clerk');
console.log('□ Copied production keys (pk_live_ and sk_live_)');
console.log('');
console.log('Vercel Setup:');
console.log('□ Connected repository to Vercel');
console.log('□ Set all environment variables in Vercel');
console.log('□ Verified build settings (Next.js framework)');
console.log('');
console.log('Testing:');
console.log('□ Test local build with: npm run build');
console.log('□ Test authentication flow after deployment');
console.log('□ Test waitlist functionality');

console.log('\n✅ All checks passed! Ready for deployment.');
console.log('\nNext steps:');
console.log('1. Push your code to your Git repository');
console.log('2. Deploy to Vercel');
console.log('3. Set environment variables in Vercel dashboard');
console.log('4. Test your production deployment');
